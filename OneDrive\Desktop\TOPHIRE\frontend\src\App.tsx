import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from './store/store';
import { checkAuthStatus } from './store/slices/authSlice';

// Layout Components
import Layout from './components/Layout/Layout';
import PublicLayout from './components/Layout/PublicLayout';

// Public Pages
import LandingPage from './pages/LandingPage';
import LoginPage from './pages/Auth/LoginPage';
import RegisterPage from './pages/Auth/RegisterPage';

// Protected Pages
import DashboardPage from './pages/Dashboard/DashboardPage';
import JobsPage from './pages/Jobs/JobsPage';
import JobDetailsPage from './pages/Jobs/JobDetailsPage';
import ProfilePage from './pages/Profile/ProfilePage';
import ApplicationsPage from './pages/Applications/ApplicationsPage';

// Admin Pages
import AdminDashboard from './pages/Admin/AdminDashboard';
import UserManagement from './pages/Admin/UserManagement';
import JobModeration from './pages/Admin/JobModeration';

// HR Pages
import HRDashboard from './pages/HR/HRDashboard';
import PostJobPage from './pages/HR/PostJobPage';
import ManageJobsPage from './pages/HR/ManageJobsPage';
import ApplicantsPage from './pages/HR/ApplicantsPage';

// Components
import LoadingSpinner from './components/UI/LoadingSpinner';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import RoleBasedRoute from './components/Auth/RoleBasedRoute';

// Types
import { UserRole } from './types/auth';

function App() {
  const dispatch = useDispatch();
  const { isAuthenticated, isLoading, user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Check authentication status on app load only if token exists
    const token = localStorage.getItem('token');
    if (token) {
      dispatch(checkAuthStatus() as any);
    }
  }, [dispatch]);

  // Show loading spinner while checking authentication (only if we have a token)
  if (isLoading && localStorage.getItem('token')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<PublicLayout />}>
          <Route index element={<LandingPage />} />
          <Route 
            path="login" 
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginPage />
              )
            } 
          />
          <Route 
            path="register" 
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <RegisterPage />
              )
            } 
          />
        </Route>

        {/* Protected Routes */}
        <Route 
          path="/" 
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          {/* Common Protected Routes */}
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="jobs" element={<JobsPage />} />
          <Route path="jobs/:id" element={<JobDetailsPage />} />
          <Route path="profile" element={<ProfilePage />} />
          <Route path="applications" element={<ApplicationsPage />} />

          {/* Admin Only Routes */}
          <Route 
            path="admin" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.ADMIN]}>
                <AdminDashboard />
              </RoleBasedRoute>
            } 
          />
          <Route 
            path="admin/users" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.ADMIN]}>
                <UserManagement />
              </RoleBasedRoute>
            } 
          />
          <Route 
            path="admin/jobs" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.ADMIN]}>
                <JobModeration />
              </RoleBasedRoute>
            } 
          />

          {/* HR Only Routes */}
          <Route 
            path="hr" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.HR]}>
                <HRDashboard />
              </RoleBasedRoute>
            } 
          />
          <Route 
            path="hr/post-job" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.HR]}>
                <PostJobPage />
              </RoleBasedRoute>
            } 
          />
          <Route 
            path="hr/manage-jobs" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.HR]}>
                <ManageJobsPage />
              </RoleBasedRoute>
            } 
          />
          <Route 
            path="hr/applicants" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.HR]}>
                <ApplicantsPage />
              </RoleBasedRoute>
            } 
          />
          <Route 
            path="hr/applicants/:jobId" 
            element={
              <RoleBasedRoute allowedRoles={[UserRole.HR]}>
                <ApplicantsPage />
              </RoleBasedRoute>
            } 
          />
        </Route>

        {/* Catch all route - 404 */}
        <Route 
          path="*" 
          element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
                <h2 className="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
                <p className="text-gray-600 mb-8">
                  The page you're looking for doesn't exist.
                </p>
                <button
                  onClick={() => window.history.back()}
                  className="btn-primary mr-4"
                >
                  Go Back
                </button>
                <a href="/" className="btn-outline">
                  Go Home
                </a>
              </div>
            </div>
          } 
        />
      </Routes>
    </div>
  );
}

export default App;
