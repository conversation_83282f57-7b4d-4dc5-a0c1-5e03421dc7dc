<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="description" content="TOPHIRE - Smart Job Portal with AI Resume Bot. Find your dream job with intelligent matching and personalized recommendations." />
    <meta name="keywords" content="jobs, career, resume, AI, job portal, hiring, recruitment" />
    <meta name="author" content="TOPHIRE Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://tophire.com/" />
    <meta property="og:title" content="TOPHIRE - Smart Job Portal with AI Resume Bot" />
    <meta property="og:description" content="Find your dream job with intelligent matching and personalized recommendations." />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://tophire.com/" />
    <meta property="twitter:title" content="TOPHIRE - Smart Job Portal with AI Resume Bot" />
    <meta property="twitter:description" content="Find your dream job with intelligent matching and personalized recommendations." />
    <meta property="twitter:image" content="%PUBLIC_URL%/twitter-image.png" />

    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="%PUBLIC_URL%/favicon.svg" />
    <link rel="icon" type="image/png" href="%PUBLIC_URL%/favicon.png" />
    
    <title>TOPHIRE - Smart Job Portal with AI Resume Bot</title>
  </head>
  <body class="bg-gray-50 font-sans antialiased">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold text-gray-900">Loading TOPHIRE...</h2>
        <p class="text-gray-600 mt-2">Preparing your smart job portal experience</p>
      </div>
    </div>

    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingScreen.remove();
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
